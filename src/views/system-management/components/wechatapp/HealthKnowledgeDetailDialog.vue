<template>
  <el-dialog
    v-model="visible"
    title="健康知识详情"
    width="800px"
    align-center
    :before-close="handleClose"
    class="health-knowledge-detail-dialog"
  >
    <div class="dialog-content" v-loading="loading">
      <div v-if="detailData" class="knowledge-detail">
        <!-- 标题 -->
        <div class="detail-section">
          <h3 class="section-title">标题</h3>
          <div class="section-content">
            {{ detailData.title }}
          </div>
        </div>

        <!-- 摘要 -->
        <div class="detail-section">
          <h3 class="section-title">摘要</h3>
          <div class="section-content">
            {{ detailData.summary }}
          </div>
        </div>

        <!-- 内容 -->
        <div class="detail-section">
          <h3 class="section-title">内容</h3>
          <div class="section-content content-text">
            {{ detailData.content }}
          </div>
        </div>

        <!-- 时间信息 -->
        <div class="detail-section">
          <h3 class="section-title">时间信息</h3>
          <div class="time-info">
            <div class="time-row">
              <span class="time-label">创建时间：</span>
              <span class="time-value">{{ formatTime(detailData.created_at) }}</span>
            </div>
            <div class="time-row">
              <span class="time-label">更新时间：</span>
              <span class="time-value">{{ formatTime(detailData.updated_at) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          关闭
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { get } from '@/utils/request'
import { format } from 'date-fns'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  knowledgeData: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const detailData = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(visible, async (newVal) => {
  if (newVal && props.knowledgeData) {
    await fetchKnowledgeDetail()
  }
})

// 获取健康知识详情
const fetchKnowledgeDetail = async () => {
  if (!props.knowledgeData?.rid) return

  loading.value = true
  try {
    const data = await get(`maternity-center/health/knowledge/detail/${props.knowledgeData.rid}/`)
    detailData.value = data
  } catch (error) {
    console.error('获取健康知识详情失败:', error)
    ElMessage.error('获取详情失败')
    handleClose()
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  try {
    return format(new Date(timeStr), 'yyyy-MM-dd HH:mm:ss')
  } catch {
    return timeStr
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  detailData.value = null
}
</script>

<style scoped>
.health-knowledge-detail-dialog {
  --el-dialog-padding-primary: 0;
}

.dialog-content {
  padding: 24px;
}

.knowledge-detail {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.detail-section {
  border-bottom: 1px solid #f3f4f6;
  padding-bottom: 16px;
}

.detail-section:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.section-title {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 12px;
}

.section-content {
  color: #1f2937;
  line-height: 1.6;
}

.content-text {
  white-space: pre-wrap;
  font-size: 14px;
  line-height: 1.8;
  min-height: 100px;
}

.time-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.time-row {
  display: flex;
  align-items: center;
  font-size: 14px;
}

.time-label {
  color: #6b7280;
  width: 80px;
  flex-shrink: 0;
}

.time-value {
  color: #1f2937;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>
