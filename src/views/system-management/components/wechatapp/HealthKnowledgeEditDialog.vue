<template>
  <el-dialog
    v-model="visible"
    :title="isEdit ? '编辑健康知识' : '新增健康知识'"
    width="900px"
    align-center
    :before-close="handleClose"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="health-knowledge-edit-dialog"
  >
    <div class="dialog-content">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="80px"
        label-position="top"
        class="knowledge-form"
      >
        <!-- 标题 -->
        <el-form-item label="标题" prop="title">
          <el-input
            v-model="formData.title"
            placeholder="请输入健康知识标题"
            maxlength="100"
            show-word-limit
            clearable
          />
        </el-form-item>

        <!-- 摘要 -->
        <el-form-item label="摘要" prop="summary">
          <el-input
            v-model="formData.summary"
            type="textarea"
            placeholder="请输入健康知识摘要"
            :rows="3"
            maxlength="200"
            show-word-limit
            resize="none"
          />
        </el-form-item>

        <!-- 内容 -->
        <el-form-item label="内容" prop="content">
          <el-input
            v-model="formData.content"
            type="textarea"
            placeholder="请输入健康知识详细内容"
            :rows="12"
            maxlength="5000"
            show-word-limit
            resize="vertical"
          />
        </el-form-item>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" size="large">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleSubmit" 
          :loading="submitting"
          size="large"
          class="bg-pink-500 hover:bg-pink-600 border-pink-500"
        >
          {{ submitting ? '保存中...' : (isEdit ? '保存修改' : '创建知识') }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { get, post, put } from '@/utils/request'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  knowledgeData: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'success'])

// 响应式数据
const formRef = ref()
const submitting = ref(false)
const loading = ref(false)

// 表单数据
const formData = ref({
  title: '',
  summary: '',
  content: ''
})

// 表单验证规则
const formRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' },
    { min: 1, max: 100, message: '标题长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  summary: [
    { required: true, message: '请输入摘要', trigger: 'blur' },
    { min: 1, max: 200, message: '摘要长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入内容', trigger: 'blur' },
    { min: 1, max: 5000, message: '内容长度在 1 到 5000 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 监听对话框打开
watch(visible, async (newVal) => {
  if (newVal) {
    await nextTick()
    if (props.isEdit && props.knowledgeData) {
      await loadKnowledgeDetail()
    } else {
      resetForm()
    }
  }
})

// 加载健康知识详情（编辑时）
const loadKnowledgeDetail = async () => {
  if (!props.knowledgeData?.rid) return

  loading.value = true
  try {
    const data = await get(`maternity-center/health/knowledge/detail/${props.knowledgeData.rid}/`)
    formData.value = {
      title: data.title || '',
      summary: data.summary || '',
      content: data.content || ''
    }
  } catch (error) {
    console.error('获取健康知识详情失败:', error)
    ElMessage.error('获取详情失败')
    handleClose()
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    title: '',
    summary: '',
    content: ''
  }
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
  } catch {
    return
  }

  submitting.value = true

  try {
    let data
    if (props.isEdit) {
      // 更新健康知识
      data = await put(`maternity-center/health/knowledge/update/${props.knowledgeData.rid}/`, formData.value)
    } else {
      // 创建健康知识
      data = await post('maternity-center/health/knowledge/create/', formData.value)
    }

    emit('success', data)
    handleClose()
  } catch (error) {
    console.error('提交失败:', error)
    if (error.msg) {
      ElMessage.error(error.msg)
    } else {
      ElMessage.error(props.isEdit ? '更新健康知识失败' : '创建健康知识失败')
    }
  } finally {
    submitting.value = false
  }
}

// 关闭对话框
const handleClose = () => {
  resetForm()
  visible.value = false
}
</script>

<style scoped>
.health-knowledge-edit-dialog {
  --el-dialog-padding-primary: 0;
}

.dialog-content {
  @apply p-6;
}

.knowledge-form {
  @apply space-y-4;
}

.dialog-footer {
  @apply flex justify-end space-x-3;
}

:deep(.el-textarea__inner) {
  font-family: inherit;
  line-height: 1.6;
}

:deep(.el-form-item__label) {
  @apply font-medium text-gray-700;
}
</style>
