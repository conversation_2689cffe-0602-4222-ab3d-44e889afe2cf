<template>
  <div class="health-knowledge-manager">
    <div class="manager-header">
      <div class="header-info">
        <h3 class="manager-title">健康知识管理</h3>
        <p class="manager-desc">管理小程序健康知识内容，当前共 {{ knowledgeList.length }} 篇文章</p>
      </div>
      <el-button 
        type="primary" 
        @click="showCreateDialog = true"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        新增知识
      </el-button>
    </div>

    <div class="knowledge-content" v-loading="loading">
      <div v-if="knowledgeList.length === 0" class="empty-state">
        <el-empty description="暂无健康知识">
          <el-button 
            type="primary" 
            @click="showCreateDialog = true"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            创建第一篇知识
          </el-button>
        </el-empty>
      </div>

      <div v-else class="knowledge-grid">
        <div
          v-for="item in knowledgeList"
          :key="item.rid"
          class="knowledge-card"
        >
          <div class="card-content">
            <div class="card-header">
              <h4 class="knowledge-title">{{ item.title }}</h4>
              <div class="card-actions">
                <el-button
                  type="primary"
                  size="small"
                  :icon="View"
                  circle
                  @click="handleView(item)"
                  title="查看详情"
                />
                <el-button
                  type="warning"
                  size="small"
                  :icon="Edit"
                  circle
                  @click="handleEdit(item)"
                  title="编辑"
                />
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  circle
                  @click="handleDelete(item)"
                  title="删除"
                />
              </div>
            </div>

            <div class="knowledge-summary">
              {{ item.summary }}
            </div>

            <div class="card-footer">
              <div class="create-time">
                <el-icon class="time-icon">
                  <Clock />
                </el-icon>
                <span>{{ formatTime(item.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <HealthKnowledgeDetailDialog
      v-model="showDetailDialog"
      :knowledge-data="currentKnowledge"
    />

    <!-- 创建对话框 -->
    <HealthKnowledgeEditDialog
      v-model="showCreateDialog"
      :is-edit="false"
      @success="handleCreateSuccess"
    />

    <!-- 编辑对话框 -->
    <HealthKnowledgeEditDialog
      v-model="showEditDialog"
      :knowledge-data="currentKnowledge"
      :is-edit="true"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElIcon, ElButton, ElEmpty } from 'element-plus'
import { Plus, View, Edit, Delete, Clock } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request'
import { format } from 'date-fns'
import HealthKnowledgeDetailDialog from './HealthKnowledgeDetailDialog.vue'
import HealthKnowledgeEditDialog from './HealthKnowledgeEditDialog.vue'

// 响应式数据
const loading = ref(false)
const knowledgeList = ref([])
const currentKnowledge = ref(null)
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)

// 获取健康知识列表
const fetchKnowledgeList = async () => {
  loading.value = true
  try {
    const data = await get('maternity-center/health/knowledge/list/')
    knowledgeList.value = data || []
  } catch (error) {
    console.error('获取健康知识列表失败:', error)
    ElMessage.error('获取健康知识列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  try {
    return format(new Date(timeStr), 'yyyy-MM-dd HH:mm')
  } catch {
    return timeStr
  }
}

// 查看详情
const handleView = (item) => {
  currentKnowledge.value = item
  showDetailDialog.value = true
}

// 编辑
const handleEdit = (item) => {
  currentKnowledge.value = item
  showEditDialog.value = true
}

// 删除
const handleDelete = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除健康知识"${item.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    )

    await del(`maternity-center/health/knowledge/delete/${item.rid}/`)
    ElMessage.success('删除成功')
    
    // 从列表中移除
    knowledgeList.value = knowledgeList.value.filter(k => k.rid !== item.rid)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除健康知识失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 创建成功
const handleCreateSuccess = (newKnowledge) => {
  knowledgeList.value.unshift(newKnowledge)
  ElMessage.success('创建成功')
}

// 编辑成功
const handleEditSuccess = (updatedKnowledge) => {
  const index = knowledgeList.value.findIndex(k => k.rid === updatedKnowledge.rid)
  if (index !== -1) {
    knowledgeList.value[index] = updatedKnowledge
  }
  ElMessage.success('更新成功')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchKnowledgeList()
})
</script>

<style scoped>
.health-knowledge-manager {
  @apply space-y-6;
}

.manager-header {
  @apply flex justify-between items-start;
}

.header-info {
  @apply flex-1;
}

.manager-title {
  @apply text-xl font-semibold text-gray-800 mb-1;
}

.manager-desc {
  @apply text-sm text-gray-600;
}

.knowledge-content {
  @apply min-h-96;
}

.empty-state {
  @apply flex justify-center items-center h-96;
}

.knowledge-grid {
  @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
}

.knowledge-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 hover:shadow-md transition-shadow duration-200;
}

.card-content {
  @apply p-6;
}

.card-header {
  @apply flex justify-between items-start mb-3;
}

.knowledge-title {
  @apply text-lg font-medium text-gray-800 flex-1 mr-3 line-clamp-2;
}

.card-actions {
  @apply flex space-x-1 flex-shrink-0;
}

.knowledge-summary {
  @apply text-sm text-gray-600 mb-4 line-clamp-3;
}

.card-footer {
  @apply flex justify-between items-center text-xs text-gray-500;
}

.create-time {
  @apply flex items-center;
}

.time-icon {
  @apply mr-1;
}

/* 文本截断样式 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
