<template>
  <div class="health-knowledge-manager">
    <div class="manager-header">
      <div class="header-info">
        <h3 class="manager-title">健康知识管理</h3>
        <p class="manager-desc">管理小程序健康知识内容，当前共 {{ knowledgeList.length }} 篇文章</p>
      </div>
      <el-button 
        type="primary" 
        @click="showCreateDialog = true"
        class="bg-pink-500 hover:bg-pink-600 border-pink-500"
      >
        <el-icon class="mr-1">
          <Plus />
        </el-icon>
        新增知识
      </el-button>
    </div>

    <div class="knowledge-content" v-loading="loading">
      <div v-if="knowledgeList.length === 0" class="empty-state">
        <el-empty description="暂无健康知识">
          <el-button 
            type="primary" 
            @click="showCreateDialog = true"
            class="bg-pink-500 hover:bg-pink-600 border-pink-500"
          >
            创建第一篇知识
          </el-button>
        </el-empty>
      </div>

      <div v-else class="knowledge-grid">
        <div
          v-for="item in knowledgeList"
          :key="item.rid"
          class="knowledge-card"
        >
          <div class="card-content">
            <div class="card-header">
              <h4 class="knowledge-title">{{ item.title }}</h4>
              <div class="card-actions">
                <el-button
                  type="primary"
                  size="small"
                  :icon="View"
                  circle
                  @click="handleView(item)"
                  title="查看详情"
                />
                <el-button
                  type="warning"
                  size="small"
                  :icon="Edit"
                  circle
                  @click="handleEdit(item)"
                  title="编辑"
                />
                <el-button
                  type="danger"
                  size="small"
                  :icon="Delete"
                  circle
                  @click="handleDelete(item)"
                  title="删除"
                />
              </div>
            </div>

            <div class="knowledge-summary">
              {{ item.summary }}
            </div>

            <div class="card-footer">
              <div class="create-time">
                <el-icon class="time-icon">
                  <Clock />
                </el-icon>
                <span>{{ formatTime(item.created_at) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <HealthKnowledgeDetailDialog
      v-model="showDetailDialog"
      :knowledge-data="currentKnowledge"
    />

    <!-- 创建对话框 -->
    <HealthKnowledgeEditDialog
      v-model="showCreateDialog"
      :is-edit="false"
      @success="handleCreateSuccess"
    />

    <!-- 编辑对话框 -->
    <HealthKnowledgeEditDialog
      v-model="showEditDialog"
      :knowledge-data="currentKnowledge"
      :is-edit="true"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox, ElIcon, ElButton, ElEmpty } from 'element-plus'
import { Plus, View, Edit, Delete, Clock } from '@element-plus/icons-vue'
import { get, del } from '@/utils/request'
import { format } from 'date-fns'
import HealthKnowledgeDetailDialog from './HealthKnowledgeDetailDialog.vue'
import HealthKnowledgeEditDialog from './HealthKnowledgeEditDialog.vue'

// 响应式数据
const loading = ref(false)
const knowledgeList = ref([])
const currentKnowledge = ref(null)
const showDetailDialog = ref(false)
const showCreateDialog = ref(false)
const showEditDialog = ref(false)

// 获取健康知识列表
const fetchKnowledgeList = async () => {
  loading.value = true
  try {
    const data = await get('maternity-center/health/knowledge/list/')
    knowledgeList.value = data || []
  } catch (error) {
    console.error('获取健康知识列表失败:', error)
    ElMessage.error('获取健康知识列表失败')
  } finally {
    loading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  try {
    return format(new Date(timeStr), 'yyyy-MM-dd HH:mm')
  } catch {
    return timeStr
  }
}

// 查看详情
const handleView = (item) => {
  currentKnowledge.value = item
  showDetailDialog.value = true
}

// 编辑
const handleEdit = (item) => {
  currentKnowledge.value = item
  showEditDialog.value = true
}

// 删除
const handleDelete = async (item) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除健康知识"${item.title}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
      }
    )

    await del(`maternity-center/health/knowledge/delete/${item.rid}/`)
    ElMessage.success('删除成功')
    
    // 从列表中移除
    knowledgeList.value = knowledgeList.value.filter(k => k.rid !== item.rid)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除健康知识失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 创建成功
const handleCreateSuccess = (newKnowledge) => {
  knowledgeList.value.unshift(newKnowledge)
  ElMessage.success('创建成功')
}

// 编辑成功
const handleEditSuccess = (updatedKnowledge) => {
  const index = knowledgeList.value.findIndex(k => k.rid === updatedKnowledge.rid)
  if (index !== -1) {
    knowledgeList.value[index] = updatedKnowledge
  }
  ElMessage.success('更新成功')
}

// 组件挂载时获取数据
onMounted(() => {
  fetchKnowledgeList()
})
</script>

<style scoped>
.health-knowledge-manager {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-info {
  flex: 1;
}

.manager-title {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.manager-desc {
  font-size: 14px;
  color: #6b7280;
}

.knowledge-content {
  min-height: 384px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 384px;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.knowledge-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
  transition: box-shadow 0.2s ease;
}

.knowledge-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-content {
  padding: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.knowledge-title {
  font-size: 18px;
  font-weight: 500;
  color: #1f2937;
  flex: 1;
  margin-right: 12px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-actions {
  display: flex;
  gap: 4px;
  flex-shrink: 0;
}

.knowledge-summary {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #9ca3af;
}

.create-time {
  display: flex;
  align-items: center;
}

.time-icon {
  margin-right: 4px;
}
</style>
